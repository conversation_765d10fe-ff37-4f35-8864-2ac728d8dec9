import { defineConfig } from 'umi';
import path from 'path';
import MonacoWebpackPlugin from 'monaco-editor-webpack-plugin';
import fs from 'fs';
import routes from './routes';

const ENV_MODE = process.env.ENV_MODE; // 测试环境(rmtc):dev; 众测：test; 线上: prod; 开发环境: undefine
const BUILD_ENV = process.env.BUILD_ENV;
const NODE_ENV = process.env.NODE_ENV;
const CUSTOM_DOMAIN = process.env.CUSTOM_DOMAIN;
const CUSTOM_API_DOMAIN = process.env.CUSTOM_API_DOMAIN;

const publicPath =
    'electron' !== BUILD_ENV && NODE_ENV === 'production' ? 'https://fe-cdn.cdn.bcebos.com/project/qamate/' : '';

// 判断是否为线上环境
const isOnlineEnv = process.env.ENV_MODE === 'prod';
const isPreviewEnv = process.env.ENV_MODE === 'test';

// 根据环境变量确定API代理目标
const getProxyTarget = (type) => {
    // 如果设置了自定义API域名，优先使用
    if (CUSTOM_API_DOMAIN) {
        return `https://${CUSTOM_API_DOMAIN}`;
    }

    if (isOnlineEnv) {
        return 'https://qamate.baidu-int.com';
    }
    if (isPreviewEnv) {
        return 'https://pioneer.baidu-int.com';
    }
    // knowledgeManager
    if (type === 'knowledgeManager') {
        return 'http://knowledge-manager.test.megqaep.appspace.baidu.com';
    }
    // autogression
    if (type === 'autogression') {
        return 'http://autogression.sandbox.megqaep.appspace.baidu.com';
    }
    // qeRag
    if (type === 'qeRag') {
        return 'https://qerag-test.baidu-int.com';
    }
    return 'http://rmtc-offline.bcc-bdbl.baidu.com';
};

export default defineConfig({
    title: 'QAMate',
    outputPath: 'electron' === BUILD_ENV ? '../../qamate-native/core/public/front' : 'dist',
    links: [{ rel: 'icon', href: 'https://fe-cdn.bj.bcebos.com/public/qamate/icon.png' }],
    history: {
        type: 'hash'
    },
    publicPath,
    hash: true,
    esbuildMinifyIIFE: true,
    // 开发服务器配置
    devServer: {
        host: '0.0.0.0', // 允许外部访问
        port: process.env.PORT || 8000, // 默认端口
        // 如果需要 HTTPS
        https: process.env.HTTPS === 'true',
        // 自定义域名支持
        allowedHosts: CUSTOM_DOMAIN ? [CUSTOM_DOMAIN] : 'all',
    },
    define: {
        BUILD_ENV: BUILD_ENV,
        MEGA_SDK_ENV: ENV_MODE === 'prod' ? 'prod' : 'beta',
        MEGA_SDK_APPID: 'yGUx1ItoPqMl'
    },
    chainWebpack(memo) {
        memo.plugin('monaco-editor').use(new MonacoWebpackPlugin(), [
            {
                languages: ['json', 'javascript', 'typescript', 'html', 'css'],
                filename: 'static/[name].worker.js' // Worker 文件打包到 static 目录
            }
        ]);
    },
    routes,
    plugins: [require.resolve('@umijs/plugins/dist/antd')],
    mock: {},
    alias: {
        HOC: path.join(__dirname, 'src/hoc'),
        FEATURES: path.join(__dirname, 'src/features'),
        COMMON: path.join(__dirname, 'src/common'),
        RESOURCES: path.join(__dirname, 'src/resources'),
        HOOKS: path.join(__dirname, 'src/hooks'),
        PAGES: path.join(__dirname, 'src/pages'),
        LAYOUTS: path.join(__dirname, 'src/layouts'),
        PACKAGES: path.join(__dirname, 'src/packages'),
        '@step': path.resolve(__dirname, 'src/common/components/TreeComponents/Step')
    },
    antd: {},
    mfsu: {},
    proxy: {
        '/lazymind': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazymind': '/lazymind' }
        },
        '/regression': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/regression': '/regression' }
        },
        '/tree': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/tree': '/tree' }
        },
        '/lazyone': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazyone': '/lazyone' }
        },
        '/common': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/common': '/common' }
        },
        '/lazycloud': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazycloud': '/lazycloud' }
        },
        '/base': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/base': '/base' }
        },
        '/lazydevice': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazydevice': '/lazydevice' }
        },
        '/icafe': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/icafe': '/icafe' }
        },
        '/core': {
            // 'target': 'http://rmtc-offline.bcc-bdbl.baidu.com:8119',
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/core': '/core' }
        },
        '/iteration': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/iteration': '/iteration' }
        },
        '/lazyailab': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazyailab': '/lazyailab' }
        },
        '/knowledgeManager': {
            target: 'http://knowledge-manager.test.megqaep.appspace.baidu.com',
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/knowledgeManager': '/knowledgeManager' }
        },
        '/itp': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/itp': '/itp' }
        },
        '/autogression': {
            target: 'http://autogression.sandbox.megqaep.appspace.baidu.com',
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/autogression': '/autogression' }
        },
        '/auth': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./cookie' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/auth': '/auth' }
        },

        '/rag/api': {
            target: 'https://qerag-test.baidu-int.com',
            changeOrigin: true,
            secure: false,
            // 流式响应的终极配置
            selfHandleResponse: true, // 自己处理响应
            onProxyReq: (proxyReq, req) => {
                try {
                    const cookieFilePath = path.join(__dirname, '.cookie');
                    if (fs.existsSync(cookieFilePath)) {
                        const cookieContent = fs.readFileSync(cookieFilePath, 'utf8');
                        if (cookieContent.trim()) {
                            proxyReq.setHeader('Cookie', cookieContent);
                        }
                    }
                } catch (error) {
                    console.log('读取 cookie 文件时出错:', error);
                }
            },
            onProxyRes: (proxyRes, req, res) => {

                if (req.url.includes('/stream')) {
                    console.log('🌊 处理流式响应');

                    // 立即设置响应头，禁用所有缓冲
                    res.writeHead(proxyRes.statusCode, {
                        'Content-Type': proxyRes.headers['content-type'] || 'text/event-stream',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                        'Connection': 'keep-alive',
                        'Transfer-Encoding': 'chunked',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                        'X-Accel-Buffering': 'no'
                    });

                    // 禁用 Node.js 的内部缓冲
                    res.socket?.setNoDelay(true);
                    res.socket?.setTimeout(0);

                    // 逐块传输，立即刷新
                    let chunkCount = 0;
                    proxyRes.on('data', (chunk) => {
                        chunkCount++;
                        res.write(chunk);
                        // 强制刷新缓冲区
                        if (res.flush) res.flush();
                    });

                    proxyRes.on('end', () => {
                        res.end();
                    });

                    proxyRes.on('error', (err) => {
                        console.error('流式响应错误:', err);
                        res.end();
                    });
                } else {
                    // 普通响应的处理
                    res.writeHead(proxyRes.statusCode, proxyRes.headers);
                    proxyRes.pipe(res, { end: true });
                }
            },
            pathRewrite: { '^/rag/api': '/rag/api' }
        },
        '/api/tool': {
            target: 'https://kirin.baidu-int.com/',
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/api/tool': '/api/tool' }
        }
    }
});
